From 0000000000000000000000000000000000000000 Mon Sep 17 00:00:00 2001
From: jmorganca <<EMAIL>>
Date: Tue, 8 Apr 2025 20:32:07 -0700
Subject: [PATCH] add phony target ggml-cpu for all cpu variants

---
 ggml/src/CMakeLists.txt | 2 ++
 1 file changed, 2 insertions(+)

diff --git a/ggml/src/CMakeLists.txt b/ggml/src/CMakeLists.txt
index ddea5ad3..45918bf6 100644
--- a/ggml/src/CMakeLists.txt
+++ b/ggml/src/CMakeLists.txt
@@ -279,6 +279,7 @@ function(ggml_add_cpu_backend_variant tag_name)
     endforeach()
 
     ggml_add_cpu_backend_variant_impl(${tag_name})
+    add_dependencies(ggml-cpu ggml-cpu-${tag_name})
 endfunction()
 
 ggml_add_backend(CPU)
@@ -287,6 +288,7 @@ if (GGML_CPU_ALL_VARIANTS)
     if (NOT GGML_BACKEND_DL)
         message(FATAL_ERROR "GGML_CPU_ALL_VARIANTS requires GGML_BACKEND_DL")
     endif()
+    add_custom_target(ggml-cpu)
     ggml_add_cpu_backend_variant(x64)
     ggml_add_cpu_backend_variant(sse42        SSE42)
     ggml_add_cpu_backend_variant(sandybridge  SSE42 AVX)
