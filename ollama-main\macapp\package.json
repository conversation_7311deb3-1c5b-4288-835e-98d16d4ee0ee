{"name": "ollama", "productName": "Ollama", "version": "0.0.0", "description": "ollama", "main": ".webpack/main", "scripts": {"start": "electron-forge start", "package": "electron-forge package --arch universal", "package:sign": "SIGN=1 electron-forge package --arch universal", "make": "electron-forge make --arch universal", "make:sign": "SIGN=1 electron-forge make --arch universal", "publish": "SIGN=1 electron-forge publish", "lint": "eslint --ext .ts,.tsx ."}, "keywords": [], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "devDependencies": {"@babel/core": "^7.22.5", "@babel/preset-react": "^7.22.5", "@electron-forge/cli": "^6.2.1", "@electron-forge/maker-deb": "^6.2.1", "@electron-forge/maker-rpm": "^6.2.1", "@electron-forge/maker-squirrel": "^6.2.1", "@electron-forge/maker-zip": "^6.2.1", "@electron-forge/plugin-auto-unpack-natives": "^6.2.1", "@electron-forge/plugin-webpack": "^6.2.1", "@electron-forge/publisher-github": "^6.2.1", "@electron/universal": "^1.4.1", "@svgr/webpack": "^8.0.1", "@types/chmodr": "^1.0.0", "@types/node": "^20.4.0", "@types/react": "^18.2.14", "@types/react-dom": "^18.2.6", "@types/uuid": "^9.0.2", "@typescript-eslint/eslint-plugin": "^5.60.0", "@typescript-eslint/parser": "^5.60.0", "@vercel/webpack-asset-relocator-loader": "^1.7.3", "babel-loader": "^9.1.2", "chmodr": "^1.2.0", "copy-webpack-plugin": "^11.0.0", "css-loader": "^6.8.1", "electron": "25.9.2", "eslint": "^8.43.0", "eslint-plugin-import": "^2.27.5", "fork-ts-checker-webpack-plugin": "^7.3.0", "node-loader": "^2.0.0", "postcss": "^8.4.24", "postcss-import": "^15.1.0", "postcss-loader": "^7.3.3", "postcss-preset-env": "^8.5.1", "style-loader": "^3.3.3", "svg-inline-loader": "^0.8.2", "tailwindcss": "^3.3.2", "ts-loader": "^9.4.3", "ts-node": "^10.9.1", "typescript": "~4.5.4", "url-loader": "^4.1.1", "webpack": "^5.88.0", "webpack-cli": "^5.1.4", "webpack-dev-server": "^4.15.1"}, "dependencies": {"@electron/remote": "^2.0.10", "@heroicons/react": "^2.0.18", "@segment/analytics-node": "^1.0.0", "copy-to-clipboard": "^3.3.3", "electron-squirrel-startup": "^1.0.0", "electron-store": "^8.1.0", "react": "^18.2.0", "react-dom": "^18.2.0", "uuid": "^9.0.0", "winston": "^3.10.0", "winston-daily-rotate-file": "^4.7.1"}}